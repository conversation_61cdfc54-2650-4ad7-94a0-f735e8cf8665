<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_login">


    <ImageView
        android:id="@+id/ivLogo"
        android:layout_width="400dp"
        android:layout_height="400dp"
        android:layout_gravity="center"
        android:src="@drawable/ic_login_logo"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/flQrcode"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/flQrcode"
        android:layout_width="280dp"
        android:layout_height="280dp"
        android:layout_gravity="center"
        android:layout_marginBottom="24dp"
        android:background="@drawable/qr_card_background"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="@id/ivLogo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/ivLogo"
        app:layout_constraintTop_toTopOf="@id/ivLogo">

        <ImageView
            android:id="@+id/iv_qr_code"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY" />
    </FrameLayout>


    <TextView
        android:id="@+id/btnBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="50dp"
        android:background="@drawable/bg_login_back"
        android:text="@string/cancel_login"
        android:textColor="@color/lock_on_primary"
        android:gravity="center"
        android:paddingHorizontal="100dp"
        android:paddingVertical="10dp"
        android:textSize="26sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivLogo" />

    <TextView
        android:id="@+id/tv_payment_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:text="@string/login_status_waiting"
        android:textColor="@color/lock_text_primary"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="@id/flQrcode"
        app:layout_constraintStart_toStartOf="@id/flQrcode"
        app:layout_constraintTop_toBottomOf="@id/flQrcode" />

    <FrameLayout
        android:layout_width="370dp"
        android:background="@drawable/ic_login_countdown"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_height="60dp">

        <TextView
            android:id="@+id/tv_countdown_seconds"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="center"
            android:layout_marginStart="120dp"
            android:text="2:00"
            android:textColor="@color/lock_on_primary"
            android:textSize="24sp" />
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
